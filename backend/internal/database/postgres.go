package database

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"backend/internal/config"
)

// ConnectionStats holds connection pool statistics
type ConnectionStats struct {
	MaxConns             int32         `json:"max_conns"`
	TotalConns           int32         `json:"total_conns"`
	IdleConns            int32         `json:"idle_conns"`
	AcquiredConns        int32         `json:"acquired_conns"`
	ConstructingConns    int32         `json:"constructing_conns"`
	AcquireCount         int64         `json:"acquire_count"`
	AcquireDuration      time.Duration `json:"acquire_duration"`
	EmptyAcquireCount    int64         `json:"empty_acquire_count"`
	CanceledAcquireCount int64         `json:"canceled_acquire_count"`
}

// PostgresDB wraps the database connection pool with enhanced management
type PostgresDB struct {
	Pool   *pgxpool.Pool
	config *config.DatabaseConfig
	mu     sync.RWMutex
	closed bool
}

// NewPostgresDB creates a new PostgreSQL database connection with enhanced configuration
func NewPostgresDB(cfg *config.DatabaseConfig) (*PostgresDB, error) {
	// Create connection string
	connStr := cfg.ConnectionString()

	// Configure connection pool
	poolConfig, err := pgxpool.ParseConfig(connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to parse database config: %w", err)
	}

	// Enhanced pool configuration
	poolConfig.MaxConns = int32(cfg.MaxConns)
	poolConfig.MinConns = int32(cfg.MinConns)
	poolConfig.MaxConnLifetime = time.Hour * 2     // Longer lifetime for better performance
	poolConfig.MaxConnIdleTime = time.Minute * 15  // Shorter idle time to free resources
	poolConfig.HealthCheckPeriod = time.Minute * 5 // Regular health checks

	// Connection timeout settings
	poolConfig.ConnConfig.ConnectTimeout = time.Second * 10

	// Set up connection callbacks for monitoring
	poolConfig.BeforeAcquire = func(ctx context.Context, conn *pgx.Conn) bool {
		// Log slow connection acquisitions
		return true
	}

	poolConfig.AfterRelease = func(conn *pgx.Conn) bool {
		// Connection is healthy, can be reused
		return true
	}

	// Create connection pool
	pool, err := pgxpool.NewWithConfig(context.Background(), poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %w", err)
	}

	// Test the connection with retries
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	var lastErr error
	for i := 0; i < 3; i++ {
		if err := pool.Ping(ctx); err != nil {
			lastErr = err
			log.Printf("Database ping attempt %d failed: %v", i+1, err)
			time.Sleep(time.Second * 2)
			continue
		}
		lastErr = nil
		break
	}

	if lastErr != nil {
		pool.Close()
		return nil, fmt.Errorf("failed to ping database after 3 attempts: %w", lastErr)
	}

	return &PostgresDB{
		Pool:   pool,
		config: cfg,
		closed: false,
	}, nil
}

// Close closes the database connection pool safely
func (db *PostgresDB) Close() {
	db.mu.Lock()
	defer db.mu.Unlock()

	if !db.closed && db.Pool != nil {
		db.Pool.Close()
		db.closed = true
		log.Println("Database connection pool closed")
	}
}

// IsClosed returns true if the database connection is closed
func (db *PostgresDB) IsClosed() bool {
	db.mu.RLock()
	defer db.mu.RUnlock()
	return db.closed
}

// HealthCheck performs a comprehensive health check on the database
func (db *PostgresDB) HealthCheck(ctx context.Context) error {
	db.mu.RLock()
	defer db.mu.RUnlock()

	if db.closed {
		return fmt.Errorf("database connection is closed")
	}

	if db.Pool == nil {
		return fmt.Errorf("database connection pool is nil")
	}

	// Test with a simple query with timeout
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	var result int
	err := db.Pool.QueryRow(ctx, "SELECT 1").Scan(&result)
	if err != nil {
		return fmt.Errorf("database health check failed: %w", err)
	}

	if result != 1 {
		return fmt.Errorf("unexpected health check result: %d", result)
	}

	return nil
}

// GetConnectionStats returns current connection pool statistics
func (db *PostgresDB) GetConnectionStats() *ConnectionStats {
	db.mu.RLock()
	defer db.mu.RUnlock()

	if db.closed || db.Pool == nil {
		return &ConnectionStats{}
	}

	stat := db.Pool.Stat()
	return &ConnectionStats{
		MaxConns:             stat.MaxConns(),
		TotalConns:           stat.TotalConns(),
		IdleConns:            stat.IdleConns(),
		AcquiredConns:        stat.AcquiredConns(),
		ConstructingConns:    stat.ConstructingConns(),
		AcquireCount:         stat.AcquireCount(),
		AcquireDuration:      stat.AcquireDuration(),
		EmptyAcquireCount:    stat.EmptyAcquireCount(),
		CanceledAcquireCount: stat.CanceledAcquireCount(),
	}
}

// Ping tests the database connection
func (db *PostgresDB) Ping(ctx context.Context) error {
	db.mu.RLock()
	defer db.mu.RUnlock()

	if db.closed {
		return fmt.Errorf("database connection is closed")
	}

	if db.Pool == nil {
		return fmt.Errorf("database connection pool is nil")
	}

	return db.Pool.Ping(ctx)
}

// Reset closes the current pool and creates a new one (useful for connection recovery)
func (db *PostgresDB) Reset() error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if db.Pool != nil {
		db.Pool.Close()
	}

	// Create new connection pool
	newDB, err := NewPostgresDB(db.config)
	if err != nil {
		return fmt.Errorf("failed to reset database connection: %w", err)
	}

	db.Pool = newDB.Pool
	db.closed = false

	log.Println("Database connection pool reset successfully")
	return nil
}
