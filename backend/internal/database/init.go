package database

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"backend/internal/config"
	"backend/internal/migrations"
)

// InitOptions contains options for database initialization
type InitOptions struct {
	// RunMigrations determines whether to run migrations during initialization
	RunMigrations bool
	// ValidateSchema determines whether to validate the schema after initialization
	ValidateSchema bool
	// CreateDatabase determines whether to create the database if it doesn't exist
	CreateDatabase bool
	// Timeout for initialization operations
	Timeout time.Duration
	// Environment (development, testing, production)
	Environment string
}

// DefaultInitOptions returns default initialization options
func DefaultInitOptions() *InitOptions {
	return &InitOptions{
		RunMigrations:  true,
		ValidateSchema: true,
		CreateDatabase: false,
		Timeout:        time.Minute * 5,
		Environment:    "development",
	}
}

// DatabaseInitializer handles database initialization tasks
type DatabaseInitializer struct {
	config  *config.DatabaseConfig
	options *InitOptions
}

// NewDatabaseInitializer creates a new database initializer
func NewDatabaseInitializer(cfg *config.DatabaseConfig, opts *InitOptions) *DatabaseInitializer {
	if opts == nil {
		opts = DefaultInitOptions()
	}
	return &DatabaseInitializer{
		config:  cfg,
		options: opts,
	}
}

// Initialize performs complete database initialization
func (di *DatabaseInitializer) Initialize(ctx context.Context) (*PostgresDB, error) {
	log.Printf("Starting database initialization for %s environment", di.options.Environment)

	// Add timeout to context
	ctx, cancel := context.WithTimeout(ctx, di.options.Timeout)
	defer cancel()

	// Create database if requested
	if di.options.CreateDatabase {
		if err := di.createDatabaseIfNotExists(ctx); err != nil {
			return nil, fmt.Errorf("failed to create database: %w", err)
		}
	}

	// Create database connection
	db, err := NewPostgresDB(di.config)
	if err != nil {
		return nil, fmt.Errorf("failed to create database connection: %w", err)
	}

	// Run migrations if requested
	if di.options.RunMigrations {
		if err := di.runMigrations(ctx, db.Pool); err != nil {
			db.Close()
			return nil, fmt.Errorf("failed to run migrations: %w", err)
		}
	}

	// Validate schema if requested
	if di.options.ValidateSchema {
		if err := di.validateSchema(ctx, db.Pool); err != nil {
			db.Close()
			return nil, fmt.Errorf("schema validation failed: %w", err)
		}
	}

	log.Printf("Database initialization completed successfully")
	return db, nil
}

// createDatabaseIfNotExists creates the database if it doesn't exist
func (di *DatabaseInitializer) createDatabaseIfNotExists(ctx context.Context) error {
	// Create a connection to the postgres database (not the target database)
	adminConfig := *di.config
	adminConfig.Name = "postgres"

	adminConnStr := adminConfig.ConnectionString()
	adminConn, err := pgx.Connect(ctx, adminConnStr)
	if err != nil {
		return fmt.Errorf("failed to connect to admin database: %w", err)
	}
	defer adminConn.Close(ctx)

	// Check if database exists
	var exists bool
	err = adminConn.QueryRow(ctx,
		"SELECT EXISTS(SELECT 1 FROM pg_database WHERE datname = $1)",
		di.config.Name).Scan(&exists)
	if err != nil {
		return fmt.Errorf("failed to check if database exists: %w", err)
	}

	if !exists {
		log.Printf("Creating database: %s", di.config.Name)
		_, err = adminConn.Exec(ctx, fmt.Sprintf("CREATE DATABASE %s", pgx.Identifier{di.config.Name}.Sanitize()))
		if err != nil {
			return fmt.Errorf("failed to create database: %w", err)
		}
		log.Printf("Database %s created successfully", di.config.Name)
	} else {
		log.Printf("Database %s already exists", di.config.Name)
	}

	return nil
}

// runMigrations runs database migrations
func (di *DatabaseInitializer) runMigrations(ctx context.Context, pool *pgxpool.Pool) error {
	log.Println("Running database migrations...")

	migrator := migrations.NewMigrator(pool)
	if err := migrator.Up(ctx); err != nil {
		return fmt.Errorf("migration failed: %w", err)
	}

	log.Println("Database migrations completed successfully")
	return nil
}

// validateSchema validates the database schema
func (di *DatabaseInitializer) validateSchema(ctx context.Context, pool *pgxpool.Pool) error {
	log.Println("Validating database schema...")

	// Check if required tables exist
	requiredTables := []string{"todos", "schema_migrations"}

	for _, table := range requiredTables {
		var exists bool
		err := pool.QueryRow(ctx,
			"SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = $1)",
			table).Scan(&exists)
		if err != nil {
			return fmt.Errorf("failed to check table %s: %w", table, err)
		}

		if !exists {
			return fmt.Errorf("required table %s does not exist", table)
		}
	}

	// Validate todos table structure
	if err := di.validateTodosTable(ctx, pool); err != nil {
		return fmt.Errorf("todos table validation failed: %w", err)
	}

	log.Println("Database schema validation completed successfully")
	return nil
}

// validateTodosTable validates the todos table structure
func (di *DatabaseInitializer) validateTodosTable(ctx context.Context, pool *pgxpool.Pool) error {
	requiredColumns := map[string]string{
		"id":          "uuid",
		"title":       "character varying",
		"description": "text",
		"completed":   "boolean",
		"created_at":  "timestamp with time zone",
		"updated_at":  "timestamp with time zone",
	}

	for column, expectedType := range requiredColumns {
		var dataType string
		err := pool.QueryRow(ctx, `
			SELECT data_type 
			FROM information_schema.columns 
			WHERE table_name = 'todos' AND column_name = $1
		`, column).Scan(&dataType)

		if err != nil {
			if err == pgx.ErrNoRows {
				return fmt.Errorf("required column %s does not exist in todos table", column)
			}
			return fmt.Errorf("failed to check column %s: %w", column, err)
		}

		if dataType != expectedType {
			return fmt.Errorf("column %s has type %s, expected %s", column, dataType, expectedType)
		}
	}

	return nil
}

// Reset drops and recreates the database (USE WITH CAUTION)
func (di *DatabaseInitializer) Reset(ctx context.Context) error {
	if di.options.Environment == "production" {
		return fmt.Errorf("database reset is not allowed in production environment")
	}

	log.Printf("WARNING: Resetting database %s in %s environment", di.config.Name, di.options.Environment)

	// Create admin connection
	adminConfig := *di.config
	adminConfig.Name = "postgres"

	adminConnStr := adminConfig.ConnectionString()
	adminConn, err := pgx.Connect(ctx, adminConnStr)
	if err != nil {
		return fmt.Errorf("failed to connect to admin database: %w", err)
	}
	defer adminConn.Close(ctx)

	// Terminate existing connections to the database
	_, err = adminConn.Exec(ctx, `
		SELECT pg_terminate_backend(pid)
		FROM pg_stat_activity
		WHERE datname = $1 AND pid <> pg_backend_pid()
	`, di.config.Name)
	if err != nil {
		log.Printf("Warning: failed to terminate existing connections: %v", err)
	}

	// Drop database if exists
	_, err = adminConn.Exec(ctx, fmt.Sprintf("DROP DATABASE IF EXISTS %s", pgx.Identifier{di.config.Name}.Sanitize()))
	if err != nil {
		return fmt.Errorf("failed to drop database: %w", err)
	}

	// Create database
	_, err = adminConn.Exec(ctx, fmt.Sprintf("CREATE DATABASE %s", pgx.Identifier{di.config.Name}.Sanitize()))
	if err != nil {
		return fmt.Errorf("failed to create database: %w", err)
	}

	log.Printf("Database %s reset completed", di.config.Name)
	return nil
}

// ValidateSchema validates the database schema (public method for CLI)
func (di *DatabaseInitializer) ValidateSchema(ctx context.Context, pool *pgxpool.Pool) error {
	return di.validateSchema(ctx, pool)
}
