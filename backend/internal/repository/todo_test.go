package repository

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"backend/internal/errors"
	"backend/internal/models"
)

func TestInMemoryTodoRepository_Create(t *testing.T) {
	repo := NewInMemoryTodoRepository()
	ctx := context.Background()

	todo := models.NewTodo("Test Todo", "Test Description")

	err := repo.Create(ctx, todo)
	require.NoError(t, err)

	// Verify todo was stored
	stored, err := repo.GetByID(ctx, todo.ID)
	require.NoError(t, err)
	assert.Equal(t, todo.ID, stored.ID)
	assert.Equal(t, todo.Title, stored.Title)
}

func TestInMemoryTodoRepository_CreateDuplicate(t *testing.T) {
	repo := NewInMemoryTodoRepository()
	ctx := context.Background()

	todo := models.NewTodo("Test Todo", "Test Description")

	// Create first time - should succeed
	err := repo.Create(ctx, todo)
	require.NoError(t, err)

	// Create same todo again - should fail
	err = repo.Create(ctx, todo)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "already exists")
}

func TestInMemoryTodoRepository_GetByID(t *testing.T) {
	repo := NewInMemoryTodoRepository()
	ctx := context.Background()

	todo := models.NewTodo("Test Todo", "Test Description")
	err := repo.Create(ctx, todo)
	require.NoError(t, err)

	// Get existing todo
	retrieved, err := repo.GetByID(ctx, todo.ID)
	require.NoError(t, err)
	assert.Equal(t, todo.ID, retrieved.ID)
	assert.Equal(t, todo.Title, retrieved.Title)

	// Get non-existing todo
	_, err = repo.GetByID(ctx, "non-existing-id")
	assert.Error(t, err)
	assert.Equal(t, errors.ErrTodoNotFound, err)
}

func TestInMemoryTodoRepository_GetAll(t *testing.T) {
	repo := NewInMemoryTodoRepository()
	ctx := context.Background()

	// Initially empty
	todos, err := repo.GetAll(ctx)
	require.NoError(t, err)
	assert.Empty(t, todos)

	// Add some todos
	todo1 := models.NewTodo("Todo 1", "Description 1")
	todo2 := models.NewTodo("Todo 2", "Description 2")

	err = repo.Create(ctx, todo1)
	require.NoError(t, err)
	err = repo.Create(ctx, todo2)
	require.NoError(t, err)

	// Get all todos
	todos, err = repo.GetAll(ctx)
	require.NoError(t, err)
	assert.Len(t, todos, 2)

	// Verify todos are returned (order may vary)
	ids := []string{todos[0].ID, todos[1].ID}
	assert.Contains(t, ids, todo1.ID)
	assert.Contains(t, ids, todo2.ID)
}

func TestInMemoryTodoRepository_Update(t *testing.T) {
	repo := NewInMemoryTodoRepository()
	ctx := context.Background()

	todo := models.NewTodo("Original Title", "Original Description")
	err := repo.Create(ctx, todo)
	require.NoError(t, err)

	// Update todo
	todo.Title = "Updated Title"
	err = repo.Update(ctx, todo)
	require.NoError(t, err)

	// Verify update
	updated, err := repo.GetByID(ctx, todo.ID)
	require.NoError(t, err)
	assert.Equal(t, "Updated Title", updated.Title)
}

func TestInMemoryTodoRepository_UpdateNonExisting(t *testing.T) {
	repo := NewInMemoryTodoRepository()
	ctx := context.Background()

	todo := models.NewTodo("Test Todo", "Test Description")

	// Try to update non-existing todo
	err := repo.Update(ctx, todo)
	assert.Error(t, err)
	assert.Equal(t, errors.ErrTodoNotFound, err)
}

func TestInMemoryTodoRepository_Delete(t *testing.T) {
	repo := NewInMemoryTodoRepository()
	ctx := context.Background()

	todo := models.NewTodo("Test Todo", "Test Description")
	err := repo.Create(ctx, todo)
	require.NoError(t, err)

	// Delete todo
	err = repo.Delete(ctx, todo.ID)
	require.NoError(t, err)

	// Verify deletion
	_, err = repo.GetByID(ctx, todo.ID)
	assert.Error(t, err)
	assert.Equal(t, errors.ErrTodoNotFound, err)
}

func TestInMemoryTodoRepository_DeleteNonExisting(t *testing.T) {
	repo := NewInMemoryTodoRepository()
	ctx := context.Background()

	// Try to delete non-existing todo
	err := repo.Delete(ctx, "non-existing-id")
	assert.Error(t, err)
	assert.Equal(t, errors.ErrTodoNotFound, err)
}
