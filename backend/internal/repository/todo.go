package repository

import (
	"context"
	"sync"

	"backend/internal/errors"
	"backend/internal/models"
)

// TodoRepository defines the interface for todo data operations
type TodoRepository interface {
	Create(ctx context.Context, todo *models.Todo) error
	GetByID(ctx context.Context, id string) (*models.Todo, error)
	GetAll(ctx context.Context) ([]*models.Todo, error)
	Update(ctx context.Context, todo *models.Todo) error
	Delete(ctx context.Context, id string) error
}

// InMemoryTodoRepository implements TodoRepository using in-memory storage
type InMemoryTodoRepository struct {
	mu    sync.RWMutex
	todos map[string]*models.Todo
}

// NewInMemoryTodoRepository creates a new in-memory todo repository
func NewInMemoryTodoRepository() *InMemoryTodoRepository {
	return &InMemoryTodoRepository{
		todos: make(map[string]*models.Todo),
	}
}

// <PERSON><PERSON> adds a new todo to the repository
func (r *InMemoryTodoRepository) Create(ctx context.Context, todo *models.Todo) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	// Check if todo with same ID already exists
	if _, exists := r.todos[todo.ID]; exists {
		return errors.NewBadRequestError("todo with this ID already exists")
	}

	r.todos[todo.ID] = todo
	return nil
}

// GetByID retrieves a todo by its ID
func (r *InMemoryTodoRepository) GetByID(ctx context.Context, id string) (*models.Todo, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	todo, exists := r.todos[id]
	if !exists {
		return nil, errors.ErrTodoNotFound
	}

	// Return a copy to prevent external modifications
	todoCopy := *todo
	return &todoCopy, nil
}

// GetAll retrieves all todos
func (r *InMemoryTodoRepository) GetAll(ctx context.Context) ([]*models.Todo, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	todos := make([]*models.Todo, 0, len(r.todos))
	for _, todo := range r.todos {
		// Return copies to prevent external modifications
		todoCopy := *todo
		todos = append(todos, &todoCopy)
	}

	return todos, nil
}

// Update modifies an existing todo
func (r *InMemoryTodoRepository) Update(ctx context.Context, todo *models.Todo) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.todos[todo.ID]; !exists {
		return errors.ErrTodoNotFound
	}

	r.todos[todo.ID] = todo
	return nil
}

// Delete removes a todo by its ID
func (r *InMemoryTodoRepository) Delete(ctx context.Context, id string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.todos[id]; !exists {
		return errors.ErrTodoNotFound
	}

	delete(r.todos, id)
	return nil
}
