package repository

import (
	"context"

	"backend/internal/models"
)

// TodoRepository defines the interface for todo data operations
type TodoRepository interface {
	Create(ctx context.Context, todo *models.Todo) error
	GetByID(ctx context.Context, id string) (*models.Todo, error)
	GetAll(ctx context.Context) ([]*models.Todo, error)
	Update(ctx context.Context, todo *models.Todo) error
	Delete(ctx context.Context, id string) error

	// Transaction support
	WithTransaction(ctx context.Context, fn func(repo TodoRepository) error) error
}

// TodoTransactionRepository extends TodoRepository for transaction-aware operations
type TodoTransactionRepository interface {
	TodoRepository
	// IsInTransaction returns true if this repository is operating within a transaction
	IsInTransaction() bool
}
