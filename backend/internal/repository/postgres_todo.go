package repository

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"

	apperrors "backend/internal/errors"
	"backend/internal/models"
)

// PostgresTodoRepository implements TodoRepository using PostgreSQL
type PostgresTodoRepository struct {
	db *pgxpool.Pool
	tx pgx.Tx // nil if not in transaction
}

// PostgresTodoTxRepository is a transaction-aware repository
type PostgresTodoTxRepository struct {
	*PostgresTodoRepository
}

// NewPostgresTodoRepository creates a new PostgreSQL todo repository
func NewPostgresTodoRepository(db *pgxpool.Pool) *PostgresTodoRepository {
	return &PostgresTodoRepository{db: db}
}

// Create adds a new todo to the database
func (r *PostgresTodoRepository) Create(ctx context.Context, todo *models.Todo) error {
	// Add timeout to context if not already set
	ctx, cancel := ensureTimeout(ctx, 5*time.Second)
	defer cancel()

	query := `
		INSERT INTO todos (id, title, description, completed, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6)
	`

	executor := r.getExecutor()
	_, err := executor.Exec(ctx, query,
		todo.ID,
		todo.Title,
		todo.Description,
		todo.Completed,
		todo.CreatedAt,
		todo.UpdatedAt,
	)

	if err != nil {
		return r.handleDatabaseError("create todo", err, map[string]interface{}{
			"todo_id": todo.ID,
			"title":   todo.Title,
		})
	}

	return nil
}

// GetByID retrieves a todo by its ID
func (r *PostgresTodoRepository) GetByID(ctx context.Context, id string) (*models.Todo, error) {
	// Add timeout to context if not already set
	ctx, cancel := ensureTimeout(ctx, 3*time.Second)
	defer cancel()

	query := `
		SELECT id, title, description, completed, created_at, updated_at
		FROM todos
		WHERE id = $1
	`

	var todo models.Todo
	executor := r.getExecutor()
	err := executor.QueryRow(ctx, query, id).Scan(
		&todo.ID,
		&todo.Title,
		&todo.Description,
		&todo.Completed,
		&todo.CreatedAt,
		&todo.UpdatedAt,
	)

	if err != nil {
		return nil, r.handleDatabaseError("get todo by ID", err, map[string]interface{}{
			"todo_id": id,
		})
	}

	return &todo, nil
}

// GetAll retrieves all todos
func (r *PostgresTodoRepository) GetAll(ctx context.Context) ([]*models.Todo, error) {
	// Add timeout to context if not already set
	ctx, cancel := ensureTimeout(ctx, 10*time.Second)
	defer cancel()

	query := `
		SELECT id, title, description, completed, created_at, updated_at
		FROM todos
		ORDER BY created_at DESC
	`

	executor := r.getExecutor()
	rows, err := executor.Query(ctx, query)
	if err != nil {
		return nil, r.handleDatabaseError("get all todos", err, nil)
	}
	defer rows.Close()

	var todos []*models.Todo
	for rows.Next() {
		var todo models.Todo
		err := rows.Scan(
			&todo.ID,
			&todo.Title,
			&todo.Description,
			&todo.Completed,
			&todo.CreatedAt,
			&todo.UpdatedAt,
		)
		if err != nil {
			return nil, r.handleDatabaseError("scan todo row", err, map[string]interface{}{
				"row_count": len(todos),
			})
		}
		todos = append(todos, &todo)
	}

	if err := rows.Err(); err != nil {
		return nil, r.handleDatabaseError("iterate todo rows", err, map[string]interface{}{
			"total_rows": len(todos),
		})
	}

	return todos, nil
}

// Update modifies an existing todo
func (r *PostgresTodoRepository) Update(ctx context.Context, todo *models.Todo) error {
	// Add timeout to context if not already set
	ctx, cancel := ensureTimeout(ctx, 5*time.Second)
	defer cancel()

	query := `
		UPDATE todos
		SET title = $2, description = $3, completed = $4, updated_at = $5
		WHERE id = $1
	`

	executor := r.getExecutor()
	result, err := executor.Exec(ctx, query,
		todo.ID,
		todo.Title,
		todo.Description,
		todo.Completed,
		todo.UpdatedAt,
	)

	if err != nil {
		return r.handleDatabaseError("update todo", err, map[string]interface{}{
			"todo_id": todo.ID,
			"title":   todo.Title,
		})
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return apperrors.ErrTodoNotFound
	}

	return nil
}

// Delete removes a todo by its ID
func (r *PostgresTodoRepository) Delete(ctx context.Context, id string) error {
	// Add timeout to context if not already set
	ctx, cancel := ensureTimeout(ctx, 3*time.Second)
	defer cancel()

	query := `DELETE FROM todos WHERE id = $1`

	executor := r.getExecutor()
	result, err := executor.Exec(ctx, query, id)
	if err != nil {
		return r.handleDatabaseError("delete todo", err, map[string]interface{}{
			"todo_id": id,
		})
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return apperrors.ErrTodoNotFound
	}

	return nil
}

// WithTransaction executes a function within a database transaction
func (r *PostgresTodoRepository) WithTransaction(ctx context.Context, fn func(repo TodoRepository) error) error {
	// If already in a transaction, just execute the function
	if r.tx != nil {
		return fn(r)
	}

	// Add timeout to context if not already set
	ctx, cancel := ensureTimeout(ctx, 30*time.Second)
	defer cancel()

	// Begin transaction
	tx, err := r.db.Begin(ctx)
	if err != nil {
		return r.handleDatabaseError("begin transaction", err, nil)
	}

	// Create transaction-aware repository
	txRepo := &PostgresTodoRepository{
		db: r.db,
		tx: tx,
	}

	// Execute function with transaction repository
	err = fn(txRepo)
	if err != nil {
		// Rollback on error
		if rollbackErr := tx.Rollback(ctx); rollbackErr != nil {
			return r.handleDatabaseError("rollback transaction", rollbackErr, map[string]interface{}{
				"original_error": err.Error(),
			})
		}
		return err
	}

	// Commit transaction
	if err := tx.Commit(ctx); err != nil {
		return r.handleDatabaseError("commit transaction", err, nil)
	}

	return nil
}

// IsInTransaction returns true if this repository is operating within a transaction
func (r *PostgresTodoRepository) IsInTransaction() bool {
	return r.tx != nil
}

// getExecutor returns the appropriate executor (transaction or pool)
func (r *PostgresTodoRepository) getExecutor() interface {
	Exec(ctx context.Context, sql string, arguments ...interface{}) (pgconn.CommandTag, error)
	Query(ctx context.Context, sql string, args ...interface{}) (pgx.Rows, error)
	QueryRow(ctx context.Context, sql string, args ...interface{}) pgx.Row
} {
	if r.tx != nil {
		return r.tx
	}
	return r.db
}

// ensureTimeout adds a timeout to context if not already set
func ensureTimeout(ctx context.Context, timeout time.Duration) (context.Context, context.CancelFunc) {
	if deadline, ok := ctx.Deadline(); ok {
		// Context already has a deadline, check if it's reasonable
		if time.Until(deadline) > timeout {
			// Existing deadline is too far, set a shorter one
			return context.WithTimeout(ctx, timeout)
		}
		// Use existing deadline
		return ctx, func() {}
	}
	// No deadline set, add one
	return context.WithTimeout(ctx, timeout)
}

// handleDatabaseError provides enhanced error handling for database operations
func (r *PostgresTodoRepository) handleDatabaseError(operation string, err error, metadata map[string]interface{}) error {
	if err == nil {
		return nil
	}

	// Check for specific PostgreSQL errors
	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) {
		switch pgErr.Code {
		case "23505": // unique_violation
			return apperrors.NewConflictError(fmt.Sprintf("resource already exists: %s", pgErr.Detail))
		case "23503": // foreign_key_violation
			return apperrors.NewBadRequestError(fmt.Sprintf("foreign key constraint violation: %s", pgErr.Detail))
		case "23502": // not_null_violation
			return apperrors.NewBadRequestError(fmt.Sprintf("required field missing: %s", pgErr.ColumnName))
		case "23514": // check_violation
			return apperrors.NewBadRequestError(fmt.Sprintf("constraint violation: %s", pgErr.Detail))
		case "08000", "08003", "08006": // connection errors
			return apperrors.ErrDatabaseUnavailable
		case "57014": // query_canceled (timeout)
			return apperrors.ErrDatabaseTimeout
		default:
			// Log the specific PostgreSQL error for debugging
			return apperrors.NewDatabaseError(operation, fmt.Errorf("postgres error %s: %s", pgErr.Code, pgErr.Message))
		}
	}

	// Check for context errors
	if errors.Is(err, context.DeadlineExceeded) {
		return apperrors.ErrDatabaseTimeout
	}
	if errors.Is(err, context.Canceled) {
		return apperrors.NewInternalServerError("operation was canceled", err)
	}

	// Check for pgx specific errors
	if errors.Is(err, pgx.ErrNoRows) {
		return apperrors.ErrTodoNotFound
	}

	// Generic database error
	return apperrors.NewDatabaseError(operation, err)
}

// isUniqueViolation checks if the error is a unique constraint violation
func isUniqueViolation(err error) bool {
	if err == nil {
		return false
	}

	// Check for PostgreSQL unique violation error code (23505)
	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) {
		return pgErr.Code == "23505"
	}

	return false
}
