package repository

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"

	apperrors "backend/internal/errors"
	"backend/internal/models"
)

// PostgresTodoRepository implements TodoRepository using PostgreSQL
type PostgresTodoRepository struct {
	db *pgxpool.Pool
}

// NewPostgresTodoRepository creates a new PostgreSQL todo repository
func NewPostgresTodoRepository(db *pgxpool.Pool) *PostgresTodoRepository {
	return &PostgresTodoRepository{db: db}
}

// Create adds a new todo to the database
func (r *PostgresTodoRepository) Create(ctx context.Context, todo *models.Todo) error {
	// Add timeout to context if not already set
	ctx, cancel := ensureTimeout(ctx, 5*time.Second)
	defer cancel()

	query := `
		INSERT INTO todos (id, title, description, completed, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6)
	`

	_, err := r.db.Exec(ctx, query,
		todo.ID,
		todo.Title,
		todo.Description,
		todo.Completed,
		todo.CreatedAt,
		todo.UpdatedAt,
	)

	if err != nil {
		return r.handleDatabaseError("create todo", err, map[string]interface{}{
			"todo_id": todo.ID,
			"title":   todo.Title,
		})
	}

	return nil
}

// GetByID retrieves a todo by its ID
func (r *PostgresTodoRepository) GetByID(ctx context.Context, id string) (*models.Todo, error) {
	query := `
		SELECT id, title, description, completed, created_at, updated_at
		FROM todos
		WHERE id = $1
	`

	var todo models.Todo
	err := r.db.QueryRow(ctx, query, id).Scan(
		&todo.ID,
		&todo.Title,
		&todo.Description,
		&todo.Completed,
		&todo.CreatedAt,
		&todo.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, apperrors.ErrTodoNotFound
		}
		return nil, apperrors.NewInternalServerError("failed to get todo", err)
	}

	return &todo, nil
}

// GetAll retrieves all todos
func (r *PostgresTodoRepository) GetAll(ctx context.Context) ([]*models.Todo, error) {
	query := `
		SELECT id, title, description, completed, created_at, updated_at
		FROM todos
		ORDER BY created_at DESC
	`

	rows, err := r.db.Query(ctx, query)
	if err != nil {
		return nil, apperrors.NewInternalServerError("failed to get todos", err)
	}
	defer rows.Close()

	var todos []*models.Todo
	for rows.Next() {
		var todo models.Todo
		err := rows.Scan(
			&todo.ID,
			&todo.Title,
			&todo.Description,
			&todo.Completed,
			&todo.CreatedAt,
			&todo.UpdatedAt,
		)
		if err != nil {
			return nil, apperrors.NewInternalServerError("failed to scan todo", err)
		}
		todos = append(todos, &todo)
	}

	if err := rows.Err(); err != nil {
		return nil, apperrors.NewInternalServerError("error iterating todos", err)
	}

	return todos, nil
}

// Update modifies an existing todo
func (r *PostgresTodoRepository) Update(ctx context.Context, todo *models.Todo) error {
	query := `
		UPDATE todos
		SET title = $2, description = $3, completed = $4, updated_at = $5
		WHERE id = $1
	`

	result, err := r.db.Exec(ctx, query,
		todo.ID,
		todo.Title,
		todo.Description,
		todo.Completed,
		todo.UpdatedAt,
	)

	if err != nil {
		return apperrors.NewInternalServerError("failed to update todo", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return apperrors.ErrTodoNotFound
	}

	return nil
}

// Delete removes a todo by its ID
func (r *PostgresTodoRepository) Delete(ctx context.Context, id string) error {
	query := `DELETE FROM todos WHERE id = $1`

	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return apperrors.NewInternalServerError("failed to delete todo", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return apperrors.ErrTodoNotFound
	}

	return nil
}

// ensureTimeout adds a timeout to context if not already set
func ensureTimeout(ctx context.Context, timeout time.Duration) (context.Context, context.CancelFunc) {
	if deadline, ok := ctx.Deadline(); ok {
		// Context already has a deadline, check if it's reasonable
		if time.Until(deadline) > timeout {
			// Existing deadline is too far, set a shorter one
			return context.WithTimeout(ctx, timeout)
		}
		// Use existing deadline
		return ctx, func() {}
	}
	// No deadline set, add one
	return context.WithTimeout(ctx, timeout)
}

// handleDatabaseError provides enhanced error handling for database operations
func (r *PostgresTodoRepository) handleDatabaseError(operation string, err error, context map[string]interface{}) error {
	if err == nil {
		return nil
	}

	// Check for specific PostgreSQL errors
	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) {
		switch pgErr.Code {
		case "23505": // unique_violation
			return apperrors.NewConflictError(fmt.Sprintf("resource already exists: %s", pgErr.Detail))
		case "23503": // foreign_key_violation
			return apperrors.NewBadRequestError(fmt.Sprintf("foreign key constraint violation: %s", pgErr.Detail))
		case "23502": // not_null_violation
			return apperrors.NewBadRequestError(fmt.Sprintf("required field missing: %s", pgErr.ColumnName))
		case "23514": // check_violation
			return apperrors.NewBadRequestError(fmt.Sprintf("constraint violation: %s", pgErr.Detail))
		case "08000", "08003", "08006": // connection errors
			return apperrors.ErrDatabaseUnavailable
		case "57014": // query_canceled (timeout)
			return apperrors.ErrDatabaseTimeout
		default:
			// Log the specific PostgreSQL error for debugging
			return apperrors.NewDatabaseError(operation, fmt.Errorf("postgres error %s: %s", pgErr.Code, pgErr.Message))
		}
	}

	// Check for context errors
	if errors.Is(err, context.DeadlineExceeded) {
		return apperrors.ErrDatabaseTimeout
	}
	if errors.Is(err, context.Canceled) {
		return apperrors.NewInternalServerError("operation was canceled", err)
	}

	// Check for pgx specific errors
	if errors.Is(err, pgx.ErrNoRows) {
		return apperrors.ErrTodoNotFound
	}

	// Generic database error
	return apperrors.NewDatabaseError(operation, err)
}

// isUniqueViolation checks if the error is a unique constraint violation
func isUniqueViolation(err error) bool {
	if err == nil {
		return false
	}

	// Check for PostgreSQL unique violation error code (23505)
	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) {
		return pgErr.Code == "23505"
	}

	return false
}
