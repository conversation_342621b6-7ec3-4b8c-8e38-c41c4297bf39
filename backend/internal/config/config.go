package config

import (
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
)

// Config holds the application configuration
type Config struct {
	Server   ServerConfig
	Database DatabaseConfig
}

// ServerConfig holds server-related configuration
type ServerConfig struct {
	Host string
	Port int
}

// DatabaseConfig holds database-related configuration
type DatabaseConfig struct {
	Host     string
	Port     int
	Name     string
	User     string
	Password string
	SSLMode  string
	MaxConns int
	MinConns int
}

// Load loads configuration from environment variables with defaults
func Load() *Config {
	// Try to load database configuration from db-creds.txt first
	dbConfig := loadDatabaseConfig()

	return &Config{
		Server: ServerConfig{
			Host: getEnv("SERVER_HOST", "0.0.0.0"),
			Port: getEnvAsInt("SERVER_PORT", 8080),
		},
		Database: dbConfig,
	}
}

// getEnv gets an environment variable with a default value
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt gets an environment variable as integer with a default value
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// loadDatabaseConfig loads database configuration from db-creds.txt or environment variables
func loadDatabaseConfig() DatabaseConfig {
	// First try to load from db-creds.txt file
	if dbConfig, err := loadFromCredentialsFile(); err == nil {
		return dbConfig
	}

	// Fallback to environment variables
	return DatabaseConfig{
		Host:     getEnv("DB_HOST", "localhost"),
		Port:     getEnvAsInt("DB_PORT", 5432),
		Name:     getEnv("DB_NAME", "todo_db"),
		User:     getEnv("DB_USER", "postgres"),
		Password: getEnv("DB_PASSWORD", ""),
		SSLMode:  getEnv("DB_SSLMODE", "prefer"),
		MaxConns: getEnvAsInt("DB_MAX_CONNS", 25),
		MinConns: getEnvAsInt("DB_MIN_CONNS", 5),
	}
}

// loadFromCredentialsFile loads database configuration from db-creds.txt
func loadFromCredentialsFile() (DatabaseConfig, error) {
	// Look for db-creds.txt in common locations
	credsPaths := []string{
		"db-creds.txt",
		"tmp/db-creds.txt",
		"backend/tmp/db-creds.txt",
		filepath.Join(".", "db-creds.txt"),
		filepath.Join(".", "tmp", "db-creds.txt"),
	}

	var credsContent string
	var err error

	for _, path := range credsPaths {
		if content, readErr := os.ReadFile(path); readErr == nil {
			credsContent = strings.TrimSpace(string(content))
			if credsContent != "" {
				break
			}
		}
	}

	if credsContent == "" {
		return DatabaseConfig{}, fmt.Errorf("db-creds.txt not found or empty")
	}

	// Parse the PostgreSQL connection URL
	parsedURL, err := url.Parse(credsContent)
	if err != nil {
		return DatabaseConfig{}, fmt.Errorf("failed to parse database URL: %w", err)
	}

	// Extract connection details
	config := DatabaseConfig{
		MaxConns: getEnvAsInt("DB_MAX_CONNS", 25),
		MinConns: getEnvAsInt("DB_MIN_CONNS", 5),
	}

	config.Host = parsedURL.Hostname()
	if parsedURL.Port() != "" {
		if port, err := strconv.Atoi(parsedURL.Port()); err == nil {
			config.Port = port
		}
	} else {
		config.Port = 5432 // Default PostgreSQL port
	}

	config.Name = strings.TrimPrefix(parsedURL.Path, "/")
	config.User = parsedURL.User.Username()
	if password, ok := parsedURL.User.Password(); ok {
		config.Password = password
	}

	// Extract SSL mode from query parameters
	query := parsedURL.Query()
	config.SSLMode = query.Get("sslmode")
	if config.SSLMode == "" {
		config.SSLMode = "prefer"
	}

	return config, nil
}

// ConnectionString returns the PostgreSQL connection string
func (d *DatabaseConfig) ConnectionString() string {
	return fmt.Sprintf("host=%s port=%d dbname=%s user=%s password=%s sslmode=%s",
		d.Host, d.Port, d.Name, d.User, d.Password, d.SSLMode)
}
