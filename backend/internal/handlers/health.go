package handlers

import (
	"context"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"

	"backend/internal/database"
)

// HealthHandler handles health check endpoints
type HealthHandler struct {
	db *database.PostgresDB
}

// NewHealthHandler creates a new health handler
func NewHealthHandler(db *database.PostgresDB) *HealthHandler {
	return &HealthHandler{db: db}
}

// HealthResponse represents the health check response
type HealthResponse struct {
	Status    string                     `json:"status"`
	Timestamp time.Time                  `json:"timestamp"`
	Database  DatabaseHealthInfo         `json:"database"`
	Stats     *database.ConnectionStats  `json:"connection_stats,omitempty"`
}

// DatabaseHealthInfo represents database health information
type DatabaseHealthInfo struct {
	Status      string        `json:"status"`
	Ping        bool          `json:"ping"`
	ResponseTime time.Duration `json:"response_time_ms"`
	Error       string        `json:"error,omitempty"`
}

// Health performs a basic health check
func (h *HealthHandler) Health(c echo.Context) error {
	ctx, cancel := context.WithTimeout(c.Request().Context(), 5*time.Second)
	defer cancel()

	response := HealthResponse{
		Status:    "healthy",
		Timestamp: time.Now(),
	}

	// Check database health
	start := time.Now()
	dbHealth := DatabaseHealthInfo{
		Status: "healthy",
		Ping:   true,
	}

	if err := h.db.HealthCheck(ctx); err != nil {
		dbHealth.Status = "unhealthy"
		dbHealth.Ping = false
		dbHealth.Error = err.Error()
		response.Status = "unhealthy"
	}

	dbHealth.ResponseTime = time.Since(start)
	response.Database = dbHealth

	// Set appropriate HTTP status
	statusCode := http.StatusOK
	if response.Status == "unhealthy" {
		statusCode = http.StatusServiceUnavailable
	}

	return c.JSON(statusCode, response)
}

// DetailedHealth performs a detailed health check with connection statistics
func (h *HealthHandler) DetailedHealth(c echo.Context) error {
	ctx, cancel := context.WithTimeout(c.Request().Context(), 10*time.Second)
	defer cancel()

	response := HealthResponse{
		Status:    "healthy",
		Timestamp: time.Now(),
	}

	// Check database health
	start := time.Now()
	dbHealth := DatabaseHealthInfo{
		Status: "healthy",
		Ping:   true,
	}

	if err := h.db.HealthCheck(ctx); err != nil {
		dbHealth.Status = "unhealthy"
		dbHealth.Ping = false
		dbHealth.Error = err.Error()
		response.Status = "unhealthy"
	}

	dbHealth.ResponseTime = time.Since(start)
	response.Database = dbHealth

	// Get connection statistics
	response.Stats = h.db.GetConnectionStats()

	// Set appropriate HTTP status
	statusCode := http.StatusOK
	if response.Status == "unhealthy" {
		statusCode = http.StatusServiceUnavailable
	}

	return c.JSON(statusCode, response)
}

// Ready checks if the service is ready to accept requests
func (h *HealthHandler) Ready(c echo.Context) error {
	ctx, cancel := context.WithTimeout(c.Request().Context(), 3*time.Second)
	defer cancel()

	// Check if database is accessible
	if err := h.db.Ping(ctx); err != nil {
		return c.JSON(http.StatusServiceUnavailable, map[string]interface{}{
			"status": "not ready",
			"error":  "database not accessible",
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"status": "ready",
	})
}
