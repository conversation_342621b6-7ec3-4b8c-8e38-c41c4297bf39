package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"time"

	"backend/internal/config"
	"backend/internal/database"
	"backend/internal/migrations"
)

func main() {
	var (
		command     = flag.String("command", "", "Command to run: init, migrate, validate, reset, status")
		environment = flag.String("env", "development", "Environment: development, testing, production")
		timeout     = flag.Duration("timeout", 5*time.Minute, "Timeout for operations")
		createDB    = flag.Bool("create-db", false, "Create database if it doesn't exist")
		force       = flag.Bool("force", false, "Force operation (required for destructive operations)")
	)
	flag.Parse()

	if *command == "" {
		printUsage()
		os.Exit(1)
	}

	// Load configuration
	cfg := config.Load()
	
	// Override environment-specific settings
	if *environment == "testing" {
		if testDB := os.Getenv("TEST_DB_NAME"); testDB != "" {
			cfg.Database.Name = testDB
		}
	}

	ctx, cancel := context.WithTimeout(context.Background(), *timeout)
	defer cancel()

	switch *command {
	case "init":
		if err := initializeDatabase(ctx, &cfg.Database, *environment, *createDB); err != nil {
			log.Fatalf("Database initialization failed: %v", err)
		}
	case "migrate":
		if err := runMigrations(ctx, &cfg.Database); err != nil {
			log.Fatalf("Migration failed: %v", err)
		}
	case "validate":
		if err := validateSchema(ctx, &cfg.Database); err != nil {
			log.Fatalf("Schema validation failed: %v", err)
		}
	case "reset":
		if err := resetDatabase(ctx, &cfg.Database, *environment, *force); err != nil {
			log.Fatalf("Database reset failed: %v", err)
		}
	case "status":
		if err := showStatus(ctx, &cfg.Database); err != nil {
			log.Fatalf("Status check failed: %v", err)
		}
	default:
		fmt.Printf("Unknown command: %s\n", *command)
		printUsage()
		os.Exit(1)
	}
}

func printUsage() {
	fmt.Println("Database Utility Tool")
	fmt.Println()
	fmt.Println("Usage:")
	fmt.Println("  dbutil -command=<command> [options]")
	fmt.Println()
	fmt.Println("Commands:")
	fmt.Println("  init      - Initialize database (create, migrate, validate)")
	fmt.Println("  migrate   - Run database migrations")
	fmt.Println("  validate  - Validate database schema")
	fmt.Println("  reset     - Reset database (DROP and CREATE)")
	fmt.Println("  status    - Show database status and connection info")
	fmt.Println()
	fmt.Println("Options:")
	fmt.Println("  -env string        Environment (development, testing, production) (default \"development\")")
	fmt.Println("  -timeout duration  Timeout for operations (default 5m0s)")
	fmt.Println("  -create-db         Create database if it doesn't exist")
	fmt.Println("  -force             Force operation (required for destructive operations)")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  dbutil -command=init -create-db")
	fmt.Println("  dbutil -command=migrate")
	fmt.Println("  dbutil -command=validate")
	fmt.Println("  dbutil -command=reset -env=testing -force")
	fmt.Println("  dbutil -command=status")
}

func initializeDatabase(ctx context.Context, cfg *config.DatabaseConfig, env string, createDB bool) error {
	log.Printf("Initializing database for %s environment", env)
	
	opts := &database.InitOptions{
		RunMigrations:  true,
		ValidateSchema: true,
		CreateDatabase: createDB,
		Timeout:        5 * time.Minute,
		Environment:    env,
	}
	
	initializer := database.NewDatabaseInitializer(cfg, opts)
	db, err := initializer.Initialize(ctx)
	if err != nil {
		return err
	}
	defer db.Close()
	
	log.Println("Database initialization completed successfully")
	return nil
}

func runMigrations(ctx context.Context, cfg *config.DatabaseConfig) error {
	log.Println("Running database migrations...")
	
	db, err := database.NewPostgresDB(cfg)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer db.Close()
	
	migrator := migrations.NewMigrator(db.Pool)
	if err := migrator.Up(ctx); err != nil {
		return fmt.Errorf("migration failed: %w", err)
	}
	
	log.Println("Migrations completed successfully")
	return nil
}

func validateSchema(ctx context.Context, cfg *config.DatabaseConfig) error {
	log.Println("Validating database schema...")
	
	opts := &database.InitOptions{
		RunMigrations:  false,
		ValidateSchema: true,
		CreateDatabase: false,
		Environment:    "validation",
	}
	
	initializer := database.NewDatabaseInitializer(cfg, opts)
	db, err := database.NewPostgresDB(cfg)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer db.Close()
	
	// Just run validation
	if err := initializer.ValidateSchema(ctx, db.Pool); err != nil {
		return err
	}
	
	log.Println("Schema validation completed successfully")
	return nil
}

func resetDatabase(ctx context.Context, cfg *config.DatabaseConfig, env string, force bool) error {
	if env == "production" && !force {
		return fmt.Errorf("database reset in production requires -force flag")
	}
	
	if !force {
		return fmt.Errorf("database reset requires -force flag for safety")
	}
	
	log.Printf("WARNING: Resetting database %s in %s environment", cfg.Name, env)
	
	opts := &database.InitOptions{
		Environment: env,
	}
	
	initializer := database.NewDatabaseInitializer(cfg, opts)
	if err := initializer.Reset(ctx); err != nil {
		return err
	}
	
	log.Println("Database reset completed successfully")
	return nil
}

func showStatus(ctx context.Context, cfg *config.DatabaseConfig) error {
	log.Println("Checking database status...")
	
	db, err := database.NewPostgresDB(cfg)
	if err != nil {
		fmt.Printf("❌ Database connection failed: %v\n", err)
		return nil
	}
	defer db.Close()
	
	// Test connection
	if err := db.Ping(ctx); err != nil {
		fmt.Printf("❌ Database ping failed: %v\n", err)
		return nil
	}
	
	fmt.Println("✅ Database connection successful")
	
	// Show connection info
	fmt.Printf("📊 Database: %s@%s:%d/%s\n", cfg.User, cfg.Host, cfg.Port, cfg.Name)
	fmt.Printf("🔒 SSL Mode: %s\n", cfg.SSLMode)
	
	// Show connection stats
	stats := db.GetConnectionStats()
	fmt.Printf("🔗 Connection Pool: %d/%d (idle: %d, acquired: %d)\n", 
		stats.TotalConns, stats.MaxConns, stats.IdleConns, stats.AcquiredConns)
	
	// Check health
	if err := db.HealthCheck(ctx); err != nil {
		fmt.Printf("⚠️  Health check failed: %v\n", err)
	} else {
		fmt.Println("✅ Health check passed")
	}
	
	// Check migrations
	migrator := migrations.NewMigrator(db.Pool)
	version, err := migrator.GetCurrentVersion(ctx)
	if err != nil {
		fmt.Printf("⚠️  Migration status unknown: %v\n", err)
	} else {
		fmt.Printf("📋 Migration version: %d\n", version)
	}
	
	return nil
}
