# TODO API

A modern, RESTful TODO API built with Go and Echo framework, following clean architecture principles and Go best practices.

## Features

- ✅ RESTful API endpoints for CRUD operations
- ✅ Clean architecture with separation of concerns
- ✅ PostgreSQL database integration with advanced connection pooling
- ✅ Database migrations system with embedded SQL files
- ✅ Transaction support for atomic operations
- ✅ Enhanced error handling with PostgreSQL-specific error codes
- ✅ Database health monitoring and connection statistics
- ✅ Configuration via environment variables or db-creds.txt file
- ✅ Input validation and comprehensive error handling
- ✅ Comprehensive unit and integration tests with PostgreSQL
- ✅ Graceful shutdown with proper connection cleanup
- ✅ Rate limiting and security middleware
- ✅ JSON serialization/deserialization
- ✅ UUID-based todo IDs
- ✅ Proper HTTP status codes
- ✅ Health check endpoints with detailed database status

## Project Structure

```
backend/
├── cmd/
│   └── main.go                 # Application entry point
├── internal/
│   ├── config/                 # Configuration management
│   │   └── config.go
│   ├── database/               # Database connection management
│   │   └── postgres.go
│   ├── errors/                 # Custom error types
│   │   └── errors.go
│   ├── handlers/               # HTTP handlers
│   │   ├── todo.go
│   │   └── todo_test.go
│   ├── migrations/             # Database migrations
│   │   ├── migrate.go
│   │   ├── 001_create_todos_table.up.sql
│   │   └── 001_create_todos_table.down.sql
│   ├── models/                 # Data models
│   │   ├── todo.go
│   │   └── todo_test.go
│   ├── repository/             # Data access layer
│   │   ├── todo.go             # Repository interface
│   │   └── postgres_todo.go    # PostgreSQL repository implementation
│   ├── routes/                 # Route configuration
│   │   └── routes.go
│   └── service/                # Business logic layer
│       ├── todo.go
│       └── todo_test.go
├── migrations/                 # SQL migration files
├── test/                       # Test utilities
│   └── testutil/
│       └── database.go
├── go.mod
├── go.sum
└── README.md
```

## API Endpoints

### Base URL
```
http://localhost:8080/api/v1
```

### Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/todos` | Get all todos |
| GET | `/todos/{id}` | Get a specific todo by ID |
| POST | `/todos` | Create a new todo |
| PUT | `/todos/{id}` | Update an existing todo |
| DELETE | `/todos/{id}` | Delete a todo |
| GET | `/health` | Basic health check endpoint |
| GET | `/health/detailed` | Detailed health check with connection stats |
| GET | `/health/ready` | Readiness probe for Kubernetes/Docker |

## Data Model

### Todo
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "title": "Learn Go",
  "description": "Study Go programming language",
  "completed": false,
  "created_at": "2023-12-01T10:00:00Z",
  "updated_at": "2023-12-01T10:00:00Z"
}
```

### Create Todo Request
```json
{
  "title": "Learn Go",
  "description": "Study Go programming language"
}
```

### Update Todo Request
```json
{
  "title": "Learn Go Advanced",
  "description": "Study advanced Go concepts",
  "completed": true
}
```

## Getting Started

### Prerequisites
- Go 1.21 or higher
- PostgreSQL 12 or higher
- Git

### Database Setup

The application supports two methods for database configuration:

#### Method 1: Using db-creds.txt file (Recommended)

1. **Create a `db-creds.txt` file** in the project root or `tmp/` directory:
```
postgresql://username:password@host:port/database?sslmode=require
```

Example:
```
postgresql://todo_user:your_password@localhost:5432/todo_db?sslmode=prefer
```

#### Method 2: Using Environment Variables

```bash
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=todo_db
export DB_USER=todo_user
export DB_PASSWORD=your_password
export DB_SSLMODE=prefer
```

**Note**: The db-creds.txt file takes precedence over environment variables if both are present.

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd backend
```

2. Install dependencies:
```bash
go mod tidy
```

3. Set up the database (see Database Setup above)

4. Run the application:
```bash
go run cmd/main.go
```

The server will start on `http://localhost:8080` and automatically run database migrations.

### Configuration

The application supports flexible configuration through multiple methods:

#### Configuration Priority (highest to lowest)
1. **db-creds.txt file** - PostgreSQL connection URL
2. **Environment variables** - Individual database settings
3. **Default values** - Fallback configuration

#### Server Configuration
| Variable | Default | Description |
|----------|---------|-------------|
| `SERVER_HOST` | `0.0.0.0` | Server host address |
| `SERVER_PORT` | `8080` | Server port |

#### Database Configuration
| Variable | Default | Description |
|----------|---------|-------------|
| `DB_HOST` | `localhost` | PostgreSQL host |
| `DB_PORT` | `5432` | PostgreSQL port |
| `DB_NAME` | `todo_db` | Database name |
| `DB_USER` | `postgres` | Database username |
| `DB_PASSWORD` | `` | Database password |
| `DB_SSLMODE` | `prefer` | SSL mode (disable, require, prefer) |
| `DB_MAX_CONNS` | `25` | Maximum database connections |
| `DB_MIN_CONNS` | `5` | Minimum database connections |

#### Database Connection Features
- **Connection Pooling**: Configurable min/max connections with health monitoring
- **Connection Lifecycle**: 2-hour max lifetime, 15-minute idle timeout
- **Health Checks**: Automatic connection health monitoring every 5 minutes
- **Retry Logic**: Automatic retry on connection failures with exponential backoff
- **Transaction Support**: Full ACID transaction support for complex operations

Example:
```bash
export SERVER_HOST=localhost
export SERVER_PORT=3000
go run cmd/main.go
```

## Database Schema

### Todos Table

```sql
CREATE TABLE todos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL CHECK (length(trim(title)) > 0),
    description TEXT DEFAULT '',
    completed BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_todos_completed ON todos(completed);
CREATE INDEX idx_todos_created_at ON todos(created_at);
CREATE INDEX idx_todos_updated_at ON todos(updated_at);
```

### Migrations

The application uses an embedded migration system that automatically runs when the application starts. Migration files are located in `internal/migrations/`:

- `001_create_todos_table.up.sql` - Creates the todos table
- `001_create_todos_table.down.sql` - Drops the todos table

To manually run migrations:
```bash
# The application automatically runs migrations on startup
# No manual intervention needed
```

## API Usage Examples

### Create a Todo
```bash
curl -X POST http://localhost:8080/api/v1/todos \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Learn Go",
    "description": "Study Go programming language"
  }'
```

### Get All Todos
```bash
curl http://localhost:8080/api/v1/todos
```

### Get a Specific Todo
```bash
curl http://localhost:8080/api/v1/todos/{todo-id}
```

### Update a Todo
```bash
curl -X PUT http://localhost:8080/api/v1/todos/{todo-id} \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Learn Go Advanced",
    "completed": true
  }'
```

### Delete a Todo
```bash
curl -X DELETE http://localhost:8080/api/v1/todos/{todo-id}
```

### Health Check
```bash
# Basic health check
curl http://localhost:8080/api/v1/health

# Detailed health check with connection statistics
curl http://localhost:8080/api/v1/health/detailed

# Readiness probe (for Kubernetes/Docker)
curl http://localhost:8080/api/v1/health/ready
```

Example health check response:
```json
{
  "status": "healthy",
  "timestamp": "2023-12-01T10:00:00Z",
  "database": {
    "status": "healthy",
    "ping": true,
    "response_time_ms": 2500000
  },
  "connection_stats": {
    "max_conns": 25,
    "total_conns": 5,
    "idle_conns": 4,
    "acquired_conns": 1,
    "acquire_count": 150,
    "acquire_duration": 1200000
  }
}
```

## Testing

Run all tests:
```bash
go test ./...
```

Run tests with coverage:
```bash
go test -cover ./...
```

Run tests with verbose output:
```bash
go test -v ./...
```

### PostgreSQL Integration Tests

All tests now use PostgreSQL exclusively. To run tests:

#### Method 1: Using db-creds.txt (Recommended)
The tests will automatically use the database configuration from your `db-creds.txt` file.

```bash
# Run all tests
go test ./...

# Run specific package tests
go test ./internal/service -v
go test ./internal/handlers -v
```

#### Method 2: Using Environment Variables
```bash
# Set database connection parameters and run tests
DB_HOST=your-host DB_PORT=5432 DB_NAME=your-test-db DB_USER=your-user DB_PASSWORD=your-password DB_SSLMODE=require go test ./...
```

#### Test Features
- **Automatic Database Setup**: Tests automatically connect to PostgreSQL and run migrations
- **Transaction Isolation**: Each test runs in isolation with proper cleanup
- **Connection Pooling Tests**: Tests verify connection pool behavior and health monitoring
- **Transaction Tests**: Tests verify ACID transaction support and rollback behavior
- **Error Handling Tests**: Tests verify PostgreSQL-specific error handling and recovery

#### Skipping Database Tests
If PostgreSQL is not available, tests will be automatically skipped:
```bash
# Skip database tests
SKIP_DB_TESTS=true go test ./...
```

## Building

Build the application:
```bash
go build -o todo-api cmd/main.go
```

Run the built binary:
```bash
./todo-api
```

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- `200 OK` - Successful GET, PUT requests
- `201 Created` - Successful POST requests
- `400 Bad Request` - Invalid request body or parameters
- `404 Not Found` - Todo not found
- `422 Unprocessable Entity` - Validation errors
- `500 Internal Server Error` - Server errors

Error response format:
```json
{
  "error": true,
  "message": "Error description"
}
```

## Validation Rules

### Todo Title
- Required
- Minimum length: 1 character
- Maximum length: 200 characters

### Todo Description
- Optional
- Maximum length: 1000 characters

## Architecture

This application follows clean architecture principles with PostgreSQL as the exclusive data store:

1. **Models Layer**: Defines data structures and business entities with validation
2. **Repository Layer**: Handles PostgreSQL data persistence with connection pooling and transactions
3. **Service Layer**: Contains business logic, validation, and transaction orchestration
4. **Handler Layer**: Manages HTTP requests/responses with comprehensive error handling
5. **Routes Layer**: Configures API endpoints, middleware, and health monitoring
6. **Database Layer**: Advanced PostgreSQL connection management with health monitoring

## Dependencies

- [Echo](https://echo.labstack.com/) - High performance, minimalist Go web framework
- [pgx](https://github.com/jackc/pgx) - PostgreSQL driver and toolkit
- [UUID](https://github.com/google/uuid) - UUID generation
- [Validator](https://github.com/go-playground/validator) - Struct validation
- [Testify](https://github.com/stretchr/testify) - Testing toolkit

## Recent Enhancements

- [x] **PostgreSQL-Only Implementation**: Removed in-memory storage, PostgreSQL is now the exclusive backend
- [x] **Advanced Connection Management**: Enhanced connection pooling with health monitoring and statistics
- [x] **Transaction Support**: Full ACID transaction support for complex operations
- [x] **Enhanced Error Handling**: PostgreSQL-specific error codes and comprehensive error messages
- [x] **Health Monitoring**: Multiple health check endpoints with detailed database status
- [x] **Flexible Configuration**: Support for both db-creds.txt files and environment variables
- [x] **Comprehensive Testing**: All tests now use PostgreSQL with proper isolation and cleanup

## Future Enhancements

- [ ] Authentication and authorization (JWT/OAuth2)
- [ ] Pagination for todo lists with cursor-based pagination
- [ ] Advanced filtering and sorting capabilities
- [ ] Todo categories/tags with hierarchical organization
- [ ] Due dates and reminders with notification system
- [ ] API documentation with OpenAPI/Swagger
- [ ] Docker containerization with multi-stage builds
- [ ] Structured logging with correlation IDs
- [ ] Metrics and monitoring (Prometheus/Grafana)
- [ ] Database backup and recovery automation
- [ ] Read replicas support for scaling
- [ ] Caching layer (Redis) for performance
- [ ] Full-text search with PostgreSQL FTS
- [ ] Rate limiting per user/API key
- [ ] Audit logging for compliance
- [ ] Database connection failover and circuit breaker
