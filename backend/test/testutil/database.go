package testutil

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/require"

	"backend/internal/config"
	"backend/internal/database"
	"backend/internal/migrations"
	"backend/internal/repository"
)

// SetupTestDB sets up a test database and returns a repository
func SetupTestDB(t *testing.T) (repository.TodoRepository, func()) {
	// Check if we should use PostgreSQL for testing
	if os.Getenv("USE_POSTGRES_TESTS") == "true" || os.Getenv("DATABASE_URL") != "" {
		return setupPostgresTestDB(t)
	}
	
	// Default to in-memory repository for tests
	repo := repository.NewInMemoryTodoRepository()
	return repo, func() {} // No cleanup needed for in-memory
}

// setupPostgresTestDB sets up a PostgreSQL test database
func setupPostgresTestDB(t *testing.T) (repository.TodoRepository, func()) {
	cfg := &config.DatabaseConfig{
		Host:     getEnvOrDefault("TEST_DB_HOST", "localhost"),
		Port:     5432,
		Name:     getEnvOrDefault("TEST_DB_NAME", "todo_test"),
		User:     getEnvOrDefault("TEST_DB_USER", "postgres"),
		Password: getEnvOrDefault("TEST_DB_PASSWORD", ""),
		SSLMode:  "prefer",
		MaxConns: 5,
		MinConns: 1,
	}

	// Create database connection
	db, err := database.NewPostgresDB(cfg)
	require.NoError(t, err, "Failed to connect to test database")

	// Run migrations
	migrator := migrations.NewMigrator(db.Pool)
	err = migrator.Up(context.Background())
	require.NoError(t, err, "Failed to run migrations")

	// Create repository
	repo := repository.NewPostgresTodoRepository(db.Pool)

	// Return cleanup function
	cleanup := func() {
		// Clean up test data
		_, _ = db.Pool.Exec(context.Background(), "DELETE FROM todos")
		db.Close()
	}

	return repo, cleanup
}

// CleanupTestDB cleans up test data from the database
func CleanupTestDB(t *testing.T, db *database.PostgresDB) {
	_, err := db.Pool.Exec(context.Background(), "DELETE FROM todos")
	require.NoError(t, err, "Failed to clean up test data")
}

// getEnvOrDefault gets an environment variable or returns a default value
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
