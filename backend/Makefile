# Makefile for TODO API Backend

.PHONY: help build run test clean db-init db-migrate db-validate db-reset db-status

# Default target
help:
	@echo "TODO API Backend - Available Commands:"
	@echo ""
	@echo "Build & Run:"
	@echo "  build         Build the application"
	@echo "  run           Run the application"
	@echo "  clean         Clean build artifacts"
	@echo ""
	@echo "Testing:"
	@echo "  test          Run all tests"
	@echo "  test-unit     Run unit tests only"
	@echo "  test-db       Run database tests with PostgreSQL"
	@echo "  test-coverage Run tests with coverage report"
	@echo ""
	@echo "Database Management:"
	@echo "  db-init       Initialize database (development)"
	@echo "  db-init-test  Initialize test database"
	@echo "  db-migrate    Run database migrations"
	@echo "  db-validate   Validate database schema"
	@echo "  db-reset-test Reset test database (DESTRUCTIVE)"
	@echo "  db-status     Show database status"
	@echo ""
	@echo "Development:"
	@echo "  deps          Download dependencies"
	@echo "  fmt           Format code"
	@echo "  lint          Run linter"
	@echo "  mod-tidy      Tidy go modules"

# Build targets
build:
	@echo "Building application..."
	go build -o bin/todo-api cmd/main.go

run: build
	@echo "Starting application..."
	./bin/todo-api

clean:
	@echo "Cleaning build artifacts..."
	rm -rf bin/
	go clean

# Testing targets
test:
	@echo "Running all tests..."
	go test ./...

test-unit:
	@echo "Running unit tests..."
	SKIP_DB_TESTS=true go test ./...

test-db:
	@echo "Running database tests..."
	@if [ -f "tmp/db-creds.txt" ]; then \
		echo "Using database from db-creds.txt"; \
		DB_HOST=$$(grep -o 'postgresql://[^:]*:\([^@]*\)@\([^:]*\):\([^/]*\)/\([^?]*\)' tmp/db-creds.txt | cut -d'@' -f2 | cut -d':' -f1) \
		DB_PORT=$$(grep -o 'postgresql://[^:]*:\([^@]*\)@\([^:]*\):\([^/]*\)/\([^?]*\)' tmp/db-creds.txt | cut -d'@' -f2 | cut -d':' -f2 | cut -d'/' -f1) \
		DB_NAME=$$(grep -o 'postgresql://[^:]*:\([^@]*\)@\([^:]*\):\([^/]*\)/\([^?]*\)' tmp/db-creds.txt | cut -d'/' -f4 | cut -d'?' -f1) \
		DB_USER=$$(grep -o 'postgresql://\([^:]*\):' tmp/db-creds.txt | cut -d'/' -f3 | cut -d':' -f1) \
		DB_PASSWORD=$$(grep -o 'postgresql://[^:]*:\([^@]*\)@' tmp/db-creds.txt | cut -d':' -f3 | cut -d'@' -f1) \
		DB_SSLMODE=require \
		go test ./... -v; \
	else \
		echo "No db-creds.txt found, skipping database tests"; \
		SKIP_DB_TESTS=true go test ./...; \
	fi

test-coverage:
	@echo "Running tests with coverage..."
	go test -cover ./...

# Database management targets
db-init:
	@echo "Initializing development database..."
	./scripts/init-db.sh -c init

db-init-test:
	@echo "Initializing test database..."
	./scripts/init-db.sh -e testing -c init

db-migrate:
	@echo "Running database migrations..."
	./scripts/init-db.sh migrate

db-validate:
	@echo "Validating database schema..."
	./scripts/init-db.sh validate

db-reset-test:
	@echo "Resetting test database..."
	./scripts/init-db.sh -e testing -f reset

db-status:
	@echo "Checking database status..."
	./scripts/init-db.sh status

# Development targets
deps:
	@echo "Downloading dependencies..."
	go mod download

fmt:
	@echo "Formatting code..."
	go fmt ./...

lint:
	@echo "Running linter..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not installed, running go vet instead"; \
		go vet ./...; \
	fi

mod-tidy:
	@echo "Tidying go modules..."
	go mod tidy

# Combined targets
setup: deps db-init
	@echo "Setup completed!"

dev: fmt lint test
	@echo "Development checks completed!"

ci: deps fmt lint test-coverage
	@echo "CI pipeline completed!"
