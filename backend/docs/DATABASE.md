# Database Management Guide

This guide covers database setup, management, and utilities for the TODO API backend.

## Overview

The TODO API uses PostgreSQL as its exclusive database backend with the following features:

- **Advanced Connection Pooling**: Configurable connection limits with health monitoring
- **Transaction Support**: Full ACID transaction support for complex operations
- **Migration System**: Embedded SQL migrations with version tracking
- **Health Monitoring**: Comprehensive health checks and connection statistics
- **Flexible Configuration**: Support for both environment variables and db-creds.txt files

## Database Configuration

### Method 1: Using db-creds.txt (Recommended)

Create a `db-creds.txt` file in the project root or `tmp/` directory:

```
postgresql://username:password@host:port/database?sslmode=require
```

Example:
```
postgresql://todo_user:your_password@localhost:5432/todo_db?sslmode=prefer
```

### Method 2: Using Environment Variables

```bash
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=todo_db
export DB_USER=todo_user
export DB_PASSWORD=your_password
export DB_SSLMODE=prefer
export DB_MAX_CONNS=25
export DB_MIN_CONNS=5
```

## Database Utilities

### 1. Command Line Tool (dbutil)

The `dbutil` command provides comprehensive database management:

```bash
# Build the utility
go build -o bin/dbutil ./cmd/dbutil

# Show help
./bin/dbutil -h

# Check database status
./bin/dbutil -command=status

# Initialize database
./bin/dbutil -command=init -create-db

# Run migrations
./bin/dbutil -command=migrate

# Validate schema
./bin/dbutil -command=validate

# Reset database (DESTRUCTIVE)
./bin/dbutil -command=reset -env=testing -force
```

### 2. Shell Script (init-db.sh)

User-friendly wrapper script with safety features:

```bash
# Show help
./scripts/init-db.sh --help

# Initialize development database
./scripts/init-db.sh init

# Initialize test database with creation
./scripts/init-db.sh -e testing -c init

# Run migrations in production
./scripts/init-db.sh -e production migrate

# Reset test database (requires --force)
./scripts/init-db.sh -e testing -f reset

# Check database status
./scripts/init-db.sh status
```

### 3. Makefile Targets

Convenient make targets for common operations:

```bash
# Database management
make db-init          # Initialize development database
make db-init-test     # Initialize test database
make db-migrate       # Run migrations
make db-validate      # Validate schema
make db-reset-test    # Reset test database
make db-status        # Show database status

# Development workflow
make setup            # Download deps + init database
make test-db          # Run tests with PostgreSQL
make dev              # Format + lint + test
```

## Database Schema

### Tables

#### todos
```sql
CREATE TABLE todos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL CHECK (length(trim(title)) > 0),
    description TEXT DEFAULT '',
    completed BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

#### schema_migrations
```sql
CREATE TABLE schema_migrations (
    version INTEGER PRIMARY KEY,
    applied_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

### Indexes
```sql
CREATE INDEX idx_todos_completed ON todos(completed);
CREATE INDEX idx_todos_created_at ON todos(created_at);
CREATE INDEX idx_todos_updated_at ON todos(updated_at);
```

## Migration System

### Migration Files

Migrations are stored in `internal/migrations/` as embedded SQL files:

- `001_create_todos_table.up.sql` - Forward migration
- `001_create_todos_table.down.sql` - Rollback migration

### Migration Commands

```bash
# Run all pending migrations
./bin/dbutil -command=migrate

# Check current migration version
./bin/dbutil -command=status
```

### Creating New Migrations

1. Create new migration files with incremented version number:
   - `002_add_new_feature.up.sql`
   - `002_add_new_feature.down.sql`

2. Add SQL statements for forward and rollback operations

3. Run migrations: `make db-migrate`

## Environment-Specific Setup

### Development Environment

```bash
# Initialize with database creation
make db-init

# Or manually
./scripts/init-db.sh -c init
```

### Testing Environment

```bash
# Initialize test database
make db-init-test

# Run tests with PostgreSQL
make test-db

# Reset test database when needed
make db-reset-test
```

### Production Environment

```bash
# Run migrations only (never reset)
./scripts/init-db.sh -e production migrate

# Validate schema
./scripts/init-db.sh -e production validate

# Check status
./scripts/init-db.sh -e production status
```

## Health Monitoring

### Health Check Endpoints

The application provides multiple health check endpoints:

```bash
# Basic health check
curl http://localhost:8080/api/v1/health

# Detailed health with connection stats
curl http://localhost:8080/api/v1/health/detailed

# Readiness probe
curl http://localhost:8080/api/v1/health/ready
```

### Connection Pool Monitoring

Monitor connection pool statistics:

```bash
# Show current connection stats
./bin/dbutil -command=status
```

Example output:
```
✅ Database connection successful
📊 Database: user@host:5432/database
🔒 SSL Mode: require
🔗 Connection Pool: 5/25 (idle: 4, acquired: 1)
✅ Health check passed
📋 Migration version: 1
```

## Troubleshooting

### Common Issues

1. **Connection Refused**
   ```bash
   # Check if PostgreSQL is running
   ./scripts/init-db.sh status
   
   # Verify configuration
   echo $DB_HOST $DB_PORT $DB_NAME
   ```

2. **Migration Failures**
   ```bash
   # Check migration status
   ./bin/dbutil -command=status
   
   # Validate schema
   ./bin/dbutil -command=validate
   ```

3. **Permission Errors**
   ```bash
   # Ensure user has proper permissions
   GRANT ALL PRIVILEGES ON DATABASE todo_db TO todo_user;
   ```

### Recovery Procedures

1. **Reset Development Database**
   ```bash
   make db-reset-test  # For testing only
   make db-init
   ```

2. **Fix Migration Issues**
   ```bash
   # Check current state
   ./bin/dbutil -command=status
   
   # Validate and re-run if needed
   ./bin/dbutil -command=validate
   ./bin/dbutil -command=migrate
   ```

## Best Practices

1. **Always use migrations** for schema changes
2. **Test migrations** in development before production
3. **Backup production data** before running migrations
4. **Use transactions** for complex operations
5. **Monitor connection pools** in production
6. **Validate schema** after deployments
7. **Use environment-specific databases** for testing

## Security Considerations

1. **Use SSL/TLS** in production (`sslmode=require`)
2. **Limit connection privileges** to minimum required
3. **Use connection pooling** to prevent connection exhaustion
4. **Monitor failed connection attempts**
5. **Rotate database credentials** regularly
6. **Use read-only users** for reporting/analytics
